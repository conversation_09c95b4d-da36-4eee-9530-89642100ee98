# Story 2.2: Firecrawl-Powered Content Extraction and Analysis

## Status
Draft

## Story
**As a** SEO specialist,
**I want** the system to extract clean, structured content using Firecrawl API,
**so that** I can analyze competitor content strategy with reliable, high-quality data extraction.

## Acceptance Criteria
1. Firecrawl API integration extracts full content from competitor pages while handling JavaScript rendering and anti-bot protection
2. Content cleaning automatically removes navigation, footer, sidebar, and advertisement elements to focus on main content
3. Main content area identification uses advanced algorithms to isolate primary article content from page noise
4. Heading structure extraction (H1-H6) maintains hierarchical organization with accurate text content and positioning
5. Text content extraction preserves paragraph structure, formatting, and contextual relationships between content sections
6. Image analysis extracts alt text, captions, and identifies content-relevant visual elements
7. Link analysis categorizes internal links, external links, and extracts anchor text patterns for competitive intelligence

## Tasks / Subtasks
- [ ] Set up Firecrawl API integration (AC: 1)
  - [ ] Create Firecrawl account and obtain API credentials
  - [ ] Install Firecrawl SDK and configure client
  - [ ] Create lib/scraping/firecrawl-client.ts wrapper
  - [ ] Implement authentication and request configuration
  - [ ] Set up environment variables and API key management
- [ ] Build content scraping service (AC: 1, 2)
  - [ ] Create ContentScrapingService class
  - [ ] Implement URL validation and preprocessing
  - [ ] Build scraping request configuration and options
  - [ ] Create error handling for failed scraping attempts
  - [ ] Add retry logic with exponential backoff
- [ ] Implement content cleaning and filtering (AC: 2, 3)
  - [ ] Create content cleaning algorithms to remove navigation elements
  - [ ] Build main content area detection using DOM analysis
  - [ ] Implement advertisement and sidebar removal
  - [ ] Create content quality scoring and validation
  - [ ] Add noise reduction and content purification
- [ ] Build heading structure extraction (AC: 4)
  - [ ] Create heading hierarchy parser (H1-H6)
  - [ ] Implement heading text extraction and cleaning
  - [ ] Build heading position and context tracking
  - [ ] Create heading relationship mapping
  - [ ] Add heading optimization analysis
- [ ] Implement text content extraction (AC: 5)
  - [ ] Create paragraph structure preservation
  - [ ] Build text formatting and markup extraction
  - [ ] Implement content section relationship mapping
  - [ ] Create text quality assessment and validation
  - [ ] Add content length and density analysis
- [ ] Build image analysis system (AC: 6)
  - [ ] Create image detection and extraction
  - [ ] Implement alt text and caption extraction
  - [ ] Build image relevance scoring
  - [ ] Create image optimization analysis
  - [ ] Add visual content categorization
- [ ] Implement link analysis engine (AC: 7)
  - [ ] Create internal vs external link categorization
  - [ ] Build anchor text extraction and analysis
  - [ ] Implement link context and positioning tracking
  - [ ] Create link authority and relevance scoring
  - [ ] Add link pattern analysis for competitive intelligence
- [ ] Create backup scraping providers (AC: 1)
  - [ ] Integrate ScrapingBee as backup scraper
  - [ ] Create Crawlee integration for additional fallback
  - [ ] Build provider switching and health monitoring
  - [ ] Implement failover logic and provider selection
  - [ ] Create scraping performance comparison
- [ ] Build scraped content storage (AC: 1-7)
  - [ ] Design database schema for scraped content
  - [ ] Create content versioning and change tracking
  - [ ] Implement content deduplication and similarity detection
  - [ ] Build content search and retrieval system
  - [ ] Add content analytics and reporting
- [ ] Create content analysis API endpoints (AC: 1-7)
  - [ ] Build POST /api/scraping/extract endpoint
  - [ ] Create GET /api/scraping/content/{id} endpoint
  - [ ] Implement batch content extraction
  - [ ] Add real-time scraping progress tracking
  - [ ] Create content export and download functionality

## Dev Notes

### Previous Story Insights
Story 2.1 established SERP analysis for competitor discovery. This story builds the content extraction engine that analyzes competitor pages.

### Firecrawl Integration Architecture
[Source: architecture.md#content-scraping-service]
```typescript
class ContentScrapingService {
  private firecrawl: FirecrawlClient;
  private backup: ScrapingBeeClient;
  
  async scrapeContent(url: string): Promise<ScrapedContent> {
    try {
      const content = await this.firecrawl.scrape(url, {
        formats: ['markdown', 'html'],
        includeTags: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'li'],
        excludeTags: ['nav', 'footer', 'aside', 'script'],
        waitFor: 2000
      });
      
      return this.processContent(content);
    } catch (error) {
      logger.warn('Firecrawl failed, using backup scraper');
      return this.scrapeWithBackup(url);
    }
  }
}
```

### Content Cleaning Strategy
[Source: PRD.md#functional-requirements]
- **Navigation Removal**: Strip header, footer, sidebar elements
- **Advertisement Filtering**: Remove ads and promotional content
- **Main Content Detection**: Identify primary article content area
- **Noise Reduction**: Clean up irrelevant page elements

### Database Schema for Scraped Content
[Source: architecture.md#database-schema]
```sql
CREATE TABLE competitor_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  serp_analysis_id UUID NOT NULL REFERENCES serp_analysis(id) ON DELETE CASCADE,
  url VARCHAR(500) NOT NULL,
  title VARCHAR(500),
  headings JSONB NOT NULL,
  content TEXT NOT NULL,
  word_count INTEGER NOT NULL,
  keyword_density DECIMAL(5,2) NOT NULL,
  lsi_keywords JSONB NOT NULL,
  entities JSONB NOT NULL,
  scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Heading Structure Analysis
[Source: PRD.md#functional-requirements]
- **Hierarchy Preservation**: Maintain H1-H6 structure and relationships
- **Content Extraction**: Clean heading text and context
- **Optimization Analysis**: Identify keyword usage in headings
- **Position Tracking**: Map heading locations within content

### Content Processing Pipeline
[Source: architecture.md#content-scraping-service]
```typescript
private processContent(content: any): ScrapedContent {
  return {
    title: content.title,
    headings: this.extractHeadings(content.markdown),
    content: this.cleanContent(content.markdown),
    wordCount: this.countWords(content.markdown),
    links: this.extractLinks(content.html),
    metadata: content.metadata
  };
}
```

### Image Analysis System
[Source: PRD.md#functional-requirements]
- **Alt Text Extraction**: Capture image descriptions
- **Caption Analysis**: Extract image captions and context
- **Relevance Scoring**: Assess image content relevance
- **Optimization Analysis**: Evaluate image SEO practices

### Link Analysis Engine
[Source: PRD.md#functional-requirements]
- **Link Categorization**: Internal vs external link classification
- **Anchor Text Analysis**: Extract and analyze link text patterns
- **Authority Assessment**: Evaluate link quality and relevance
- **Competitive Intelligence**: Identify linking strategies

### Anti-Bot Protection Handling
[Source: architecture.md#content-scraping]
- **JavaScript Rendering**: Full browser-based scraping
- **User Agent Rotation**: Avoid detection patterns
- **Request Throttling**: Respect rate limits and delays
- **Proxy Support**: IP rotation for large-scale scraping

### File Locations
[Source: architecture.md#frontend-application-structure]
- Scraping service: `lib/scraping/firecrawl-client.ts`
- Content processing: `lib/scraping/content-processor.ts`
- API endpoints: `app/api/scraping/`
- Data models: `types/scraping.ts`

### Required Dependencies
- @firecrawl/sdk (Firecrawl API client)
- cheerio (HTML parsing and manipulation)
- turndown (HTML to Markdown conversion)
- natural (text processing and analysis)

### Environment Variables
- FIRECRAWL_API_KEY (primary scraping service)
- SCRAPINGBEE_API_KEY (backup scraping service)
- CRAWLEE_STORAGE_DIR (local scraping storage)

### Content Quality Metrics
[Source: PRD.md#functional-requirements]
- **Content Length**: Word count and character analysis
- **Content Depth**: Paragraph and section analysis
- **Content Structure**: Heading hierarchy and organization
- **Content Quality**: Readability and coherence scoring

### Error Handling and Resilience
[Source: architecture.md#fault-tolerance]
- **Scraping Failures**: Retry logic with different providers
- **Rate Limiting**: Respect website rate limits and robots.txt
- **Content Validation**: Ensure extracted content quality
- **Graceful Degradation**: Partial results when scraping fails

### Performance Optimization
- **Concurrent Scraping**: Parallel processing of multiple URLs
- **Content Caching**: Cache scraped content to avoid re-scraping
- **Selective Scraping**: Only scrape necessary content sections
- **Memory Management**: Efficient handling of large content

### Security Considerations
[Source: architecture.md#security-implementation]
- **Respectful Scraping**: Follow robots.txt and rate limits
- **Data Sanitization**: Clean extracted content for security
- **API Key Security**: Secure storage of scraping service keys
- **Content Validation**: Prevent malicious content injection

### Testing Standards
- Unit tests for content extraction functions
- Integration tests for Firecrawl API
- Mock scraping responses in tests
- Test content cleaning and filtering
- Validate heading structure extraction
- Test backup provider failover

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-16 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record

### Agent Model Used

### Debug Log References

### Completion Notes List

### File List

## QA Results
