# Core Technology Stack

## Frontend Stack
- **Framework**: Next.js 14+ with App Router
- **Language**: TypeScript (strict mode)
- **UI Library**: React 18 with Tailwind CSS v4
- **Component Library**: Radix UI + shadcn/ui
- **State Management**: React Context + Zustand
- **Real-time**: Supabase real-time subscriptions
- **Deployment**: Vercel with Edge Functions

## Backend Stack
- **Database**: Supabase PostgreSQL with RLS
- **Authentication**: Supabase Auth with JWT
- **API Layer**: Vercel Serverless Functions + Supabase Edge Functions
- **Real-time**: Supabase Realtime API
- **File Storage**: Supabase Storage (if needed)
- **Background Jobs**: Supabase Edge Functions with cron

## AI/ML Stack
- **Primary AI**: OpenAI GPT-4+ (latest models)
- **NLP Processing**: Google Natural Language API
- **Content Analysis**: Custom algorithms + TensorFlow.js
- **Quality Assurance**: Grammarly API integration (optional)
- **Anti-Detection**: Custom prompt engineering

## External Services
- **SERP Analysis**: Serper.dev (primary), <PERSON><PERSON><PERSON><PERSON> (backup)
- **Content Scraping**: Firecrawl (primary), ScrapingBee (backup)
- **Monitoring**: Sentry, Vercel Analytics
- **Payment Processing**: Stripe + Supabase billing
- **Email**: Supabase Auth emails
